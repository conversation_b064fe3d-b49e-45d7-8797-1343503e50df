import { Blocks } from '@/pkg/lark/docx'
import { WordContext, addImageToContext, calculateImageDimensions } from '../context'
import { Paragraph, TextRun, ImageRun, AlignmentType } from 'docx'
import { extractImageDataFromDataUrl, getImageDimensions } from '../image-processor'

/**
 * 转换图片块
 */
export async function transformImage(block: Blocks, context: WordContext): Promise<Paragraph | null> {
  if (!context.options.convertImages) {
    // 如果不转换图片，返回占位文本
    const imageName = getImageName(block)
    return new Paragraph({
      children: [
        new TextRun({
          text: `[图片: ${imageName}]`,
          italics: true,
          color: '999999',
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        before: 120,
        after: 120,
      },
    })
  }

  try {
    const imageSnapshot = (block.snapshot as any)?.image
    if (!imageSnapshot) {
      console.error('🖼️ 图片转换失败: 图片数据缺失', block)
      return createImagePlaceholder('图片数据缺失')
    }

    const { token, name, dataUrl } = imageSnapshot
    if (!token) {
      console.error('🖼️ 图片转换失败: 图片token缺失', imageSnapshot)
      return createImagePlaceholder('图片token缺失')
    }

    if (!dataUrl) {
      console.error('🖼️ 图片转换失败: 图片未加载', { token, name, imageSnapshot })
      return createImagePlaceholder(`图片未加载: ${name || '未知图片'}`)
    }

    // 提取图片数据
    const imageInfo = extractImageDataFromDataUrl(dataUrl)
    if (!imageInfo) {
      return createImagePlaceholder(`图片格式不支持: ${name || '未知图片'}`)
    }

    // 获取图片尺寸
    const dimensions = await getImageDimensions(dataUrl, imageSnapshot)
    if (!dimensions) {
      return createImagePlaceholder(`无法获取图片尺寸: ${name || '未知图片'}`)
    }

    // 计算合适的尺寸
    const targetDimensions = calculateImageDimensions(
      dimensions.width,
      dimensions.height,
      context
    )

    console.error('🖼️ === Word文档图片尺寸计算 ===')
    console.error(`🖼️ 获取到的原始尺寸: width=${dimensions.width}, height=${dimensions.height}`)
    console.error(`🖼️ Word文档目标尺寸: width=${targetDimensions.width}, height=${targetDimensions.height}`)
    console.error(`🖼️ 图片名称: ${name}`)
    console.error('🖼️ === Word尺寸计算完成 ===')

    // 添加图片到上下文
    addImageToContext(context, token, name, dataUrl, targetDimensions.width, targetDimensions.height)

    // 创建图片段落
    return new Paragraph({
      children: [
        new ImageRun({
          type: imageInfo.type,
          data: imageInfo.buffer,
          transformation: {
            width: targetDimensions.width,
            height: targetDimensions.height,
          },
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        before: 120, // 图片前间距 (6pt)
        after: 120,  // 图片后间距 (6pt)
      },
    })
  } catch (error) {
    console.error('转换图片失败:', error)
    const imageName = getImageName(block)
    return createImagePlaceholder(`图片处理失败: ${imageName}`)
  }
}

/**
 * 创建图片占位符段落
 */
function createImagePlaceholder(message: string): Paragraph {
  return new Paragraph({
    children: [
      new TextRun({
        text: `[${message}]`,
        italics: true,
        color: 'FF0000',
      }),
    ],
    alignment: AlignmentType.CENTER,
    spacing: {
      before: 120,
      after: 120,
    },
  })
}

/**
 * 获取图片名称
 */
function getImageName(block: Blocks): string {
  const imageSnapshot = (block.snapshot as any)?.image
  return imageSnapshot?.name || '未知图片'
}

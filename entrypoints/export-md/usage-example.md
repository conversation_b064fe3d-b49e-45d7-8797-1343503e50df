# 飞书文档增强文件下载使用示例

## 快速开始

### 1. 在飞书文档页面打开浏览器控制台

按 `F12` 或右键选择"检查"打开开发者工具，切换到 Console 标签页。

### 2. 基础调试和测试

```javascript
// 分析当前文档中的文件类型和数量
window.analyzeDocumentFiles()

// 快速分析文件（不下载，只统计）
window.quickAnalyzeFiles()

// 完整的调试流程（包含下载测试）
window.debugFileDownload()

// 测试增强下载功能
window.testEnhancedDownload()
```

### 3. 高级使用

```javascript
// 测试单个文件下载（需要提供文件token）
window.testSingleFileDownload('your_file_token_here')

// 手动获取所有文档文件
getAllDocumentFiles().then(files => {
  console.log('获取到的文件:', files)
  console.log('文件数量:', Object.keys(files).length)
})
```

## 实际使用场景

### 场景1: 检查文档中的ZIP包

```javascript
// 1. 先分析文档
window.analyzeDocumentFiles()

// 查看控制台输出，会显示类似：
// 📊 文件类型分析结果: {
//   total: 5,
//   images: 2,
//   files: 3,
//   videos: 0,
//   zipFiles: 2,
//   others: 1,
//   fileTypes: { zip: 2, pdf: 1 }
// }

// 2. 如果有ZIP文件，测试下载
window.testEnhancedDownload()
```

### 场景2: 导出Markdown时自动使用增强下载

增强下载功能已经集成到Markdown导出流程中，会自动：

1. 检测文档中的文件类型
2. 对于ZIP包等大文件，优先使用CDN方式下载
3. 如果CDN方式失败，自动回退到原有方式
4. 提供详细的下载进度和统计信息

### 场景3: 批量文档处理

```javascript
// 获取当前文档的所有文件
const files = await getAllDocumentFiles()

// 处理每个文件
Object.entries(files).forEach(([token, base64Data]) => {
  console.log(`文件 ${token}: ${base64Data.length} 字符`)
  
  // 可以将base64数据转换为Blob进行进一步处理
  const binaryString = atob(base64Data)
  const bytes = new Uint8Array(binaryString.length)
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  const blob = new Blob([bytes])
  
  console.log(`文件大小: ${blob.size} 字节`)
})
```

## 调试输出说明

### 正常流程的输出示例

```
🔑 开始获取 jssdk-session...
✅ 从页面脚本获取到 jssdk-session

📄 开始获取文档数据, token: doccnxxxxx...
🌐 尝试主接口: https://example.feishu.cn/wiki/doccnxxxxx
📡 主接口响应: { code: 0, message: undefined }
✅ 主接口成功获取文档数据

🔍 开始提取文件 tokens...
📁 文件映射表大小: 3
🔍 检查文件: { key: "file1", type: "file", file: {...} }
📎 添加文件token: { token: "bascnxxxxx", name: "example.zip", type: "file", needsThumbnail: false, finalType: "preview" }
✅ 提取完成，共找到 3 个文件

🌐 开始批量获取文件 CDN 链接...
📊 文件数量: 3
📦 处理第 1/1 批，包含 3 个文件
📡 第 1 批响应状态: 200 OK
📊 第 1 批响应数据: { code: 0, dataLength: 3 }
✅ 第 1 批处理成功，获取到 3 个文件链接
🎉 批量获取完成，总共获取到 3 个文件的 CDN 链接

📥 开始下载文件: bascnxxxxx...
🌐 下载URL: https://sf3-cn.feishucdn.com/obj/...
📊 下载完成，文件大小: 1048576
🔓 开始解密文件...
🔑 密钥和IV长度: { keyLength: 32, ivLength: 12 }
📊 数据分离: { totalLength: 1048576, dataLength: 1048560, authTagLength: 16 }
🔑 密钥导入成功
✅ 文件解密成功，解密后大小: 1048560
✅ 文件处理完成: bascnxxxxx...

🎉 所有文件下载完成，成功下载: 3
```

### 错误情况的输出示例

```
❌ 从页面脚本获取 jssdk-session 失败: Error: ...
❌ 从 cookie 获取 jssdk-session 失败: Error: ...
⚠️ 未能获取到 jssdk-session

📄 开始获取文档数据, token: doccnxxxxx...
🌐 尝试主接口: https://example.feishu.cn/wiki/doccnxxxxx
📡 主接口响应: { code: 4000003, message: "权限不足" }
🔄 主接口失败，尝试备用接口...
```

## 常见问题解决

### Q: 显示"未能获取到 jssdk-session"

**解决方案:**
1. 确保已登录飞书账号
2. 刷新页面后重试
3. 检查是否在正确的飞书文档页面

### Q: CDN下载失败，但原有方式成功

**说明:** 这是正常情况，系统会自动回退到原有下载方式。可能的原因：
- 网络连接问题
- 文件权限变更
- CDN服务临时不可用

### Q: 文件解密失败

**可能原因:**
1. 文件已损坏或被修改
2. 密钥或IV不正确
3. 文件格式不支持

**解决方案:**
1. 重新获取文件信息
2. 检查文件是否可以正常访问
3. 尝试使用原有下载方式

### Q: ZIP文件下载不完整

**检查步骤:**
1. 查看控制台中的文件大小信息
2. 对比下载前后的文件大小
3. 检查网络连接是否稳定

```javascript
// 检查下载的文件大小
const files = await getAllDocumentFiles()
Object.entries(files).forEach(([token, data]) => {
  const size = Math.round(data.length * 0.75) // base64转二进制大约减少25%
  console.log(`文件 ${token}: ${size} 字节`)
})
```

## 性能优化建议

### 1. 批量处理
- 系统自动将文件分批处理（每批200个）
- 避免同时下载过多文件

### 2. 网络优化
- 在网络状况良好时进行大文件下载
- 如果下载失败，可以重试

### 3. 内存管理
- 大文件下载后及时处理，避免内存占用过多
- 使用完毕后清理相关数据

```javascript
// 示例：处理大文件后清理内存
const files = await getAllDocumentFiles()
// 处理文件...
files = null // 清理引用，帮助垃圾回收
```

## 技术细节

### 支持的文件类型
- **图片**: jpg, png, gif, webp, svg 等
- **文档**: pdf, doc, docx, ppt, pptx, xls, xlsx 等  
- **压缩包**: zip, rar, 7z, tar, gz, bz2 等
- **视频**: mp4, avi, mov, mkv, wmv, flv, webm 等
- **其他**: 所有飞书支持的文件类型

### 下载方式优先级
1. **CDN方式**: 使用飞书CDN接口，支持加密文件解密
2. **原有方式**: 使用现有的文件下载接口
3. **智能选择**: 根据文件类型和大小自动选择最佳方式

### 安全性
- 所有操作都在用户已有权限范围内
- 不会泄露用户凭据或敏感信息
- 遵循飞书的安全策略和限制

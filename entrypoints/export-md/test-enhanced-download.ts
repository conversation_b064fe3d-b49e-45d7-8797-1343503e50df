import { Toast } from "@/pkg/lark/env"
import { docx } from '@/pkg/lark/docx'
import { createEnhancedFileDownloader } from './enhanced-file-downloader'

/**
 * 测试增强文件下载功能
 */
export const testEnhancedDownload = async (): Promise<void> => {
  console.error('🧪 开始测试增强文件下载功能...')
  
  try {
    if (!docx.rootBlock) {
      Toast.warning({ content: '当前文档无法测试' })
      return
    }

    // 获取文档中的文件
    const { files } = docx.intoMarkdownAST({
      whiteboard: false,
      file: true,
    })

    console.error('📁 文档中的文件数量:', files.length)

    if (files.length === 0) {
      Toast.info({ content: '当前文档中没有文件' })
      return
    }

    // 分析文件类型
    const fileAnalysis = {
      total: files.length,
      zipFiles: 0,
      videoFiles: 0,
      otherFiles: 0,
      fileNames: [] as string[]
    }

    files.forEach(file => {
      const fileName = file.data?.name || 'unknown'
      fileAnalysis.fileNames.push(fileName)
      
      if (/\.(zip|rar|7z|tar|gz|bz2)$/i.test(fileName)) {
        fileAnalysis.zipFiles++
      } else if (/\.(mp4|avi|mov|mkv|wmv|flv|webm|m4v|3gp|ogv)$/i.test(fileName)) {
        fileAnalysis.videoFiles++
      } else {
        fileAnalysis.otherFiles++
      }
    })

    console.error('📊 文件分析:', fileAnalysis)

    Toast.loading({
      content: '正在测试增强文件下载...',
      keepAlive: true,
      key: 'test_download'
    })

    // 创建增强下载器
    const enhancedDownloader = createEnhancedFileDownloader()

    // 测试下载前几个文件
    const testFiles = files.slice(0, Math.min(3, files.length))
    console.error('🎯 测试文件:', testFiles.map(f => f.data?.name))

    const results = await enhancedDownloader.downloadFiles(testFiles, {
      batchSize: 1,
      onProgress: (progress) => {
        Toast.loading({
          content: `测试下载进度: ${Math.floor(progress * 100)}%`,
          keepAlive: true,
          key: 'test_download'
        })
      }
    })

    const stats = enhancedDownloader.getStats()
    
    Toast.remove('test_download')

    console.error('✅ 测试完成!')
    console.error('📊 下载统计:', stats)
    console.error('📁 下载结果:', results.map(r => ({
      filename: r.filename,
      size: r.content.size,
      method: r.downloadMethod,
      isZip: r.isZip
    })))

    // 显示测试结果
    const successCount = results.length
    const zipCount = results.filter(r => r.isZip).length
    const cdnCount = results.filter(r => r.downloadMethod === 'cdn').length

    Toast.success({
      content: `测试完成! 成功下载 ${successCount}/${testFiles.length} 个文件，其中 ${zipCount} 个ZIP包，${cdnCount} 个使用CDN方式`
    })

    // 如果有ZIP文件，特别提示
    if (zipCount > 0) {
      console.error('🎉 ZIP文件下载测试成功!')
      Toast.info({
        content: `ZIP包下载功能正常，共测试 ${zipCount} 个ZIP文件`,
        duration: 5000
      })
    }

  } catch (error) {
    Toast.remove('test_download')
    console.error('❌ 测试失败:', error)
    Toast.error({
      content: `测试失败: ${error instanceof Error ? error.message : '未知错误'}`
    })
  }
}

/**
 * 快速测试 - 只分析不下载
 */
export const quickAnalyzeFiles = (): void => {
  console.error('🔍 快速分析文档文件...')
  
  try {
    if (!docx.rootBlock) {
      Toast.warning({ content: '当前文档无法分析' })
      return
    }

    const { files } = docx.intoMarkdownAST({
      whiteboard: false,
      file: true,
    })

    const analysis = {
      total: files.length,
      zipFiles: [] as string[],
      videoFiles: [] as string[],
      otherFiles: [] as string[],
      hasDownloadFunction: 0
    }

    files.forEach(file => {
      const fileName = file.data?.name || 'unknown'
      
      if (file.data?.fetchFile) {
        analysis.hasDownloadFunction++
      }
      
      if (/\.(zip|rar|7z|tar|gz|bz2)$/i.test(fileName)) {
        analysis.zipFiles.push(fileName)
      } else if (/\.(mp4|avi|mov|mkv|wmv|flv|webm|m4v|3gp|ogv)$/i.test(fileName)) {
        analysis.videoFiles.push(fileName)
      } else {
        analysis.otherFiles.push(fileName)
      }
    })

    console.error('📊 文件分析结果:', analysis)

    const message = [
      `总计 ${analysis.total} 个文件`,
      `ZIP包 ${analysis.zipFiles.length} 个`,
      `视频 ${analysis.videoFiles.length} 个`,
      `其他 ${analysis.otherFiles.length} 个`,
      `可下载 ${analysis.hasDownloadFunction} 个`
    ].join('，')

    Toast.info({ content: message, duration: 5000 })

    if (analysis.zipFiles.length > 0) {
      console.error('📦 发现的ZIP文件:', analysis.zipFiles)
      Toast.info({
        content: `发现 ${analysis.zipFiles.length} 个ZIP包: ${analysis.zipFiles.slice(0, 3).join(', ')}${analysis.zipFiles.length > 3 ? '...' : ''}`,
        duration: 8000
      })
    }

  } catch (error) {
    console.error('❌ 分析失败:', error)
    Toast.error({
      content: `分析失败: ${error instanceof Error ? error.message : '未知错误'}`
    })
  }
}

// 将测试函数暴露到全局
declare global {
  interface Window {
    testEnhancedDownload: typeof testEnhancedDownload
    quickAnalyzeFiles: typeof quickAnalyzeFiles
  }
}

if (typeof window !== 'undefined') {
  window.testEnhancedDownload = testEnhancedDownload
  window.quickAnalyzeFiles = quickAnalyzeFiles
  
  console.error('🧪 测试函数已注册到全局:')
  console.error('  - window.testEnhancedDownload() - 测试增强下载功能')
  console.error('  - window.quickAnalyzeFiles() - 快速分析文档文件')
}

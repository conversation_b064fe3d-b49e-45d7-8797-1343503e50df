import { Toast } from "@/pkg/lark/env"
import {
  getJsSdkSession,
  getDocumentToken,
  getDocumentData,
  extractFileTokens,
  getFileCdnUrls,
  downloadAndDecryptFile,
  getAllDocumentFiles,
  isVideoFile
} from '@/pkg/lark/file'
import { SessionExtractor, analyzeSessionExtraction } from '@/pkg/lark/session-extractor'

/**
 * 深度调试 jssdk-session 获取
 */
export const debugJsSdkSession = (): void => {
  console.error('🔍 深度调试 jssdk-session 获取...')

  // 1. 检查页面基本信息
  console.error('\n=== 页面基本信息 ===')
  console.error('URL:', window.location.href)
  console.error('域名:', window.location.hostname)
  console.error('路径:', window.location.pathname)

  // 2. 检查所有脚本标签
  console.error('\n=== 脚本标签分析 ===')
  const scripts = Array.from(document.scripts)
  console.error('脚本总数:', scripts.length)

  scripts.forEach((script, index) => {
    const content = script.innerHTML
    if (content && content.length > 0) {
      console.error(`脚本 ${index + 1}:`, {
        长度: content.length,
        包含session: content.includes('session'),
        包含Session: content.includes('Session'),
        包含jssdk: content.includes('jssdk'),
        包含__: content.includes('__'),
        前100字符: content.substring(0, 100)
      })

      // 查找所有可能的session相关字符串
      const sessionMatches = content.match(/[a-zA-Z_$][a-zA-Z0-9_$]*session[a-zA-Z0-9_$]*/gi)
      if (sessionMatches) {
        console.error(`  发现session相关变量:`, sessionMatches)
      }

      // 查找所有赋值语句
      const assignments = content.match(/[a-zA-Z_$][a-zA-Z0-9_$]*\s*[=:]\s*['"][^'"]{10,}['"]/g)
      if (assignments) {
        console.error(`  发现赋值语句:`, assignments.slice(0, 5)) // 只显示前5个
      }
    }
  })

  // 3. 检查所有cookie
  console.error('\n=== Cookie 分析 ===')
  const cookies = document.cookie.split(';')
  console.error('Cookie总数:', cookies.length)
  cookies.forEach(cookie => {
    const trimmed = cookie.trim()
    if (trimmed) {
      console.error('Cookie:', trimmed)
    }
  })

  // 4. 检查localStorage
  console.error('\n=== localStorage 分析 ===')
  console.error('localStorage项目数:', localStorage.length)
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key) {
      const value = localStorage.getItem(key)
      console.error(`localStorage[${key}]:`, value?.substring(0, 100) + (value && value.length > 100 ? '...' : ''))
    }
  }

  // 5. 检查sessionStorage
  console.error('\n=== sessionStorage 分析 ===')
  console.error('sessionStorage项目数:', sessionStorage.length)
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i)
    if (key) {
      const value = sessionStorage.getItem(key)
      console.error(`sessionStorage[${key}]:`, value?.substring(0, 100) + (value && value.length > 100 ? '...' : ''))
    }
  }

  // 6. 检查全局变量
  console.error('\n=== 全局变量分析 ===')
  const globalVars = ['__jssdkSession__', 'jssdkSession', 'jssdk_session', 'session', 'Session']
  globalVars.forEach(varName => {
    try {
      const value = (window as any)[varName]
      if (value !== undefined) {
        console.error(`window.${varName}:`, typeof value, value)
      }
    } catch (e) {
      // 忽略错误
    }
  })

  // 7. 使用增强session提取器
  console.error('\n=== 增强 session 提取器分析 ===')
  const extractor = new SessionExtractor(true)
  const extractionResult = extractor.extractSession()

  console.error('提取结果:', {
    成功: extractionResult.success,
    方法: extractionResult.method,
    session长度: extractionResult.session?.length || 0,
    详情: extractionResult.details
  })

  // 8. 尝试原有方法获取session
  console.error('\n=== 原有方法获取 session ===')
  const session = getJsSdkSession()
  console.error('获取结果:', session ? `成功: ${session.substring(0, 20)}...` : '失败')

  // 9. 对比结果
  console.error('\n=== 结果对比 ===')
  console.error('增强提取器:', extractionResult.session ? '成功' : '失败')
  console.error('原有方法:', session ? '成功' : '失败')
  console.error('结果一致:', extractionResult.session === session)
}

/**
 * 调试文件下载功能
 * 在浏览器控制台中调用 window.debugFileDownload() 来测试
 */
export const debugFileDownload = async (): Promise<void> => {
  console.error('🔧 开始调试文件下载功能...')

  try {
    // 步骤1: 获取会话信息
    console.error('\n=== 步骤1: 获取会话信息 ===')
    const jsSdkSession = getJsSdkSession()
    const token = getDocumentToken()

    console.error('📋 会话信息:', {
      hasJsSdkSession: !!jsSdkSession,
      jsSdkSessionLength: jsSdkSession?.length || 0,
      jsSdkSessionPreview: jsSdkSession ? jsSdkSession.substring(0, 20) + '...' : 'null',
      documentToken: token.substring(0, 10) + '...'
    })

    if (!jsSdkSession) {
      console.error('❌ 未获取到 jssdk-session，运行深度调试...')
      debugJsSdkSession()
      return
    }

    // 步骤2: 获取文档数据
    console.error('\n=== 步骤2: 获取文档数据 ===')
    const { docVar, token: finalToken } = await getDocumentData(token, jsSdkSession)

    console.error('📄 文档数据:', {
      code: docVar.code,
      hasData: !!docVar.data,
      finalToken: finalToken.substring(0, 10) + '...'
    })

    // 步骤3: 提取文件tokens
    console.error('\n=== 步骤3: 提取文件tokens ===')
    const fileTokens = extractFileTokens(docVar)

    console.error('📁 文件tokens:', {
      count: fileTokens.length,
      tokens: fileTokens.map(ft => ({
        token: ft.file_token.substring(0, 10) + '...',
        platform: ft.platform,
        type: ft.type
      }))
    })

    if (fileTokens.length === 0) {
      console.error('⚠️ 没有找到文件，调试结束')
      Toast.warning({ content: '文档中没有找到文件' })
      return
    }

    // 步骤4: 获取CDN链接
    console.error('\n=== 步骤4: 获取CDN链接 ===')
    const cdnUrls = await getFileCdnUrls(fileTokens, jsSdkSession)

    console.error('🔗 CDN链接:', {
      count: cdnUrls.length,
      urls: cdnUrls.map(url => ({
        token: url.file_token.substring(0, 10) + '...',
        url: url.url.substring(0, 50) + '...',
        hasKey: !!url.key,
        hasIv: !!url.iv
      }))
    })

    if (cdnUrls.length === 0) {
      console.error('❌ 没有获取到CDN链接')
      Toast.error({ content: '获取CDN链接失败' })
      return
    }

    // 步骤5: 下载第一个文件作为测试
    console.error('\n=== 步骤5: 下载测试文件 ===')
    const testFile = cdnUrls[0]
    console.error('🎯 测试文件:', {
      token: testFile.file_token.substring(0, 10) + '...',
      url: testFile.url.substring(0, 50) + '...'
    })

    const fileData = await downloadAndDecryptFile(testFile, jsSdkSession)

    if (fileData) {
      console.error('✅ 文件下载成功:', {
        token: testFile.file_token.substring(0, 10) + '...',
        dataLength: fileData.length,
        dataSample: fileData.substring(0, 100) + '...'
      })

      Toast.success({
        content: `测试文件下载成功，数据长度: ${fileData.length}`
      })
    } else {
      console.error('❌ 文件下载失败')
      Toast.error({ content: '测试文件下载失败' })
    }

    // 步骤6: 完整流程测试
    console.error('\n=== 步骤6: 完整流程测试 ===')
    const allFiles = await getAllDocumentFiles()

    console.error('🎉 完整流程结果:', {
      fileCount: Object.keys(allFiles).length,
      tokens: Object.keys(allFiles).map(token => token.substring(0, 10) + '...')
    })

    Toast.success({
      content: `调试完成！成功获取 ${Object.keys(allFiles).length} 个文件`
    })

  } catch (error) {
    console.error('❌ 调试过程中出错:', error)
    Toast.error({
      content: `调试失败: ${error instanceof Error ? error.message : '未知错误'}`
    })
  }
}

/**
 * 测试单个文件下载
 */
export const testSingleFileDownload = async (fileToken: string): Promise<void> => {
  console.error('🔧 测试单个文件下载:', fileToken)

  try {
    const jsSdkSession = getJsSdkSession()

    // 构造文件信息
    const fileInfo = {
      file_token: fileToken,
      platform: "pc",
      type: "preview"
    }

    // 获取CDN链接
    const cdnUrls = await getFileCdnUrls([fileInfo], jsSdkSession)

    if (cdnUrls.length === 0) {
      throw new Error('获取CDN链接失败')
    }

    // 下载文件
    const fileData = await downloadAndDecryptFile(cdnUrls[0], jsSdkSession)

    if (fileData) {
      console.error('✅ 单个文件下载成功:', {
        token: fileToken.substring(0, 10) + '...',
        dataLength: fileData.length
      })

      Toast.success({ content: '单个文件下载测试成功' })
    } else {
      throw new Error('文件下载失败')
    }

  } catch (error) {
    console.error('❌ 单个文件下载测试失败:', error)
    Toast.error({
      content: `单个文件下载失败: ${error instanceof Error ? error.message : '未知错误'}`
    })
  }
}

/**
 * 分析文档中的文件类型
 */
export const analyzeDocumentFiles = async (): Promise<void> => {
  console.error('🔍 开始分析文档文件类型...')

  try {
    const jsSdkSession = getJsSdkSession()
    const token = getDocumentToken()
    const { docVar } = await getDocumentData(token, jsSdkSession)

    const fileMap = docVar.data?.file_map || {}
    const analysis = {
      total: 0,
      images: 0,
      files: 0,
      videos: 0,
      zipFiles: 0,
      others: 0,
      fileTypes: {} as Record<string, number>
    }

    Object.entries(fileMap).forEach(([key, file]: [string, any]) => {
      analysis.total++

      if (file.type === "image") {
        analysis.images++
      } else if (file.type === "file") {
        analysis.files++

        const fileName = file.file?.name || ''
        const extension = fileName.split('.').pop()?.toLowerCase() || 'unknown'

        analysis.fileTypes[extension] = (analysis.fileTypes[extension] || 0) + 1

        if (isVideoFile(fileName)) {
          analysis.videos++
        } else if (/\.(zip|rar|7z|tar|gz|bz2)$/i.test(fileName)) {
          analysis.zipFiles++
        } else {
          analysis.others++
        }
      }
    })

    console.error('📊 文件类型分析结果:', analysis)

    Toast.info({
      content: `文档分析完成: 总计${analysis.total}个文件，其中ZIP包${analysis.zipFiles}个`
    })

  } catch (error) {
    console.error('❌ 文档分析失败:', error)
    Toast.error({
      content: `文档分析失败: ${error instanceof Error ? error.message : '未知错误'}`
    })
  }
}

// 将调试函数暴露到全局，方便在控制台调用
declare global {
  interface Window {
    debugJsSdkSession: typeof debugJsSdkSession
    debugFileDownload: typeof debugFileDownload
    testSingleFileDownload: typeof testSingleFileDownload
    analyzeDocumentFiles: typeof analyzeDocumentFiles
  }
}

// 自动注册到全局
if (typeof window !== 'undefined') {
  window.debugJsSdkSession = debugJsSdkSession
  window.debugFileDownload = debugFileDownload
  window.testSingleFileDownload = testSingleFileDownload
  window.analyzeDocumentFiles = analyzeDocumentFiles

  console.error('🔧 调试函数已注册到全局:')
  console.error('  - window.debugJsSdkSession() - 深度调试session获取')
  console.error('  - window.debugFileDownload() - 完整调试流程')
  console.error('  - window.testSingleFileDownload(token) - 测试单个文件')
  console.error('  - window.analyzeDocumentFiles() - 分析文档文件类型')
}

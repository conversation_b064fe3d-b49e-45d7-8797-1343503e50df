import { Toast } from "@/pkg/lark/env"
import { 
  getJsSdkSession, 
  getDocumentToken, 
  getDocumentData, 
  extractFileTokens, 
  getFileCdnUrls, 
  downloadAndDecryptFile,
  getAllDocumentFiles,
  isVideoFile
} from '@/pkg/lark/file'

/**
 * 调试文件下载功能
 * 在浏览器控制台中调用 window.debugFileDownload() 来测试
 */
export const debugFileDownload = async (): Promise<void> => {
  console.error('🔧 开始调试文件下载功能...')
  
  try {
    // 步骤1: 获取会话信息
    console.error('\n=== 步骤1: 获取会话信息 ===')
    const jsSdkSession = getJsSdkSession()
    const token = getDocumentToken()
    
    console.error('📋 会话信息:', {
      hasJsSdkSession: !!jsSdkSession,
      jsSdkSessionLength: jsSdkSession?.length || 0,
      documentToken: token.substring(0, 10) + '...'
    })
    
    // 步骤2: 获取文档数据
    console.error('\n=== 步骤2: 获取文档数据 ===')
    const { docVar, token: finalToken } = await getDocumentData(token, jsSdkSession)
    
    console.error('📄 文档数据:', {
      code: docVar.code,
      hasData: !!docVar.data,
      finalToken: finalToken.substring(0, 10) + '...'
    })
    
    // 步骤3: 提取文件tokens
    console.error('\n=== 步骤3: 提取文件tokens ===')
    const fileTokens = extractFileTokens(docVar)
    
    console.error('📁 文件tokens:', {
      count: fileTokens.length,
      tokens: fileTokens.map(ft => ({
        token: ft.file_token.substring(0, 10) + '...',
        platform: ft.platform,
        type: ft.type
      }))
    })
    
    if (fileTokens.length === 0) {
      console.error('⚠️ 没有找到文件，调试结束')
      Toast.warning({ content: '文档中没有找到文件' })
      return
    }
    
    // 步骤4: 获取CDN链接
    console.error('\n=== 步骤4: 获取CDN链接 ===')
    const cdnUrls = await getFileCdnUrls(fileTokens, jsSdkSession)
    
    console.error('🔗 CDN链接:', {
      count: cdnUrls.length,
      urls: cdnUrls.map(url => ({
        token: url.file_token.substring(0, 10) + '...',
        url: url.url.substring(0, 50) + '...',
        hasKey: !!url.key,
        hasIv: !!url.iv
      }))
    })
    
    if (cdnUrls.length === 0) {
      console.error('❌ 没有获取到CDN链接')
      Toast.error({ content: '获取CDN链接失败' })
      return
    }
    
    // 步骤5: 下载第一个文件作为测试
    console.error('\n=== 步骤5: 下载测试文件 ===')
    const testFile = cdnUrls[0]
    console.error('🎯 测试文件:', {
      token: testFile.file_token.substring(0, 10) + '...',
      url: testFile.url.substring(0, 50) + '...'
    })
    
    const fileData = await downloadAndDecryptFile(testFile, jsSdkSession)
    
    if (fileData) {
      console.error('✅ 文件下载成功:', {
        token: testFile.file_token.substring(0, 10) + '...',
        dataLength: fileData.length,
        dataSample: fileData.substring(0, 100) + '...'
      })
      
      Toast.success({ 
        content: `测试文件下载成功，数据长度: ${fileData.length}` 
      })
    } else {
      console.error('❌ 文件下载失败')
      Toast.error({ content: '测试文件下载失败' })
    }
    
    // 步骤6: 完整流程测试
    console.error('\n=== 步骤6: 完整流程测试 ===')
    const allFiles = await getAllDocumentFiles()
    
    console.error('🎉 完整流程结果:', {
      fileCount: Object.keys(allFiles).length,
      tokens: Object.keys(allFiles).map(token => token.substring(0, 10) + '...')
    })
    
    Toast.success({ 
      content: `调试完成！成功获取 ${Object.keys(allFiles).length} 个文件` 
    })
    
  } catch (error) {
    console.error('❌ 调试过程中出错:', error)
    Toast.error({ 
      content: `调试失败: ${error instanceof Error ? error.message : '未知错误'}` 
    })
  }
}

/**
 * 测试单个文件下载
 */
export const testSingleFileDownload = async (fileToken: string): Promise<void> => {
  console.error('🔧 测试单个文件下载:', fileToken)
  
  try {
    const jsSdkSession = getJsSdkSession()
    
    // 构造文件信息
    const fileInfo = {
      file_token: fileToken,
      platform: "pc",
      type: "preview"
    }
    
    // 获取CDN链接
    const cdnUrls = await getFileCdnUrls([fileInfo], jsSdkSession)
    
    if (cdnUrls.length === 0) {
      throw new Error('获取CDN链接失败')
    }
    
    // 下载文件
    const fileData = await downloadAndDecryptFile(cdnUrls[0], jsSdkSession)
    
    if (fileData) {
      console.error('✅ 单个文件下载成功:', {
        token: fileToken.substring(0, 10) + '...',
        dataLength: fileData.length
      })
      
      Toast.success({ content: '单个文件下载测试成功' })
    } else {
      throw new Error('文件下载失败')
    }
    
  } catch (error) {
    console.error('❌ 单个文件下载测试失败:', error)
    Toast.error({ 
      content: `单个文件下载失败: ${error instanceof Error ? error.message : '未知错误'}` 
    })
  }
}

/**
 * 分析文档中的文件类型
 */
export const analyzeDocumentFiles = async (): Promise<void> => {
  console.error('🔍 开始分析文档文件类型...')
  
  try {
    const jsSdkSession = getJsSdkSession()
    const token = getDocumentToken()
    const { docVar } = await getDocumentData(token, jsSdkSession)
    
    const fileMap = docVar.data?.file_map || {}
    const analysis = {
      total: 0,
      images: 0,
      files: 0,
      videos: 0,
      zipFiles: 0,
      others: 0,
      fileTypes: {} as Record<string, number>
    }
    
    Object.entries(fileMap).forEach(([key, file]: [string, any]) => {
      analysis.total++
      
      if (file.type === "image") {
        analysis.images++
      } else if (file.type === "file") {
        analysis.files++
        
        const fileName = file.file?.name || ''
        const extension = fileName.split('.').pop()?.toLowerCase() || 'unknown'
        
        analysis.fileTypes[extension] = (analysis.fileTypes[extension] || 0) + 1
        
        if (isVideoFile(fileName)) {
          analysis.videos++
        } else if (/\.(zip|rar|7z|tar|gz|bz2)$/i.test(fileName)) {
          analysis.zipFiles++
        } else {
          analysis.others++
        }
      }
    })
    
    console.error('📊 文件类型分析结果:', analysis)
    
    Toast.info({ 
      content: `文档分析完成: 总计${analysis.total}个文件，其中ZIP包${analysis.zipFiles}个` 
    })
    
  } catch (error) {
    console.error('❌ 文档分析失败:', error)
    Toast.error({ 
      content: `文档分析失败: ${error instanceof Error ? error.message : '未知错误'}` 
    })
  }
}

// 将调试函数暴露到全局，方便在控制台调用
declare global {
  interface Window {
    debugFileDownload: typeof debugFileDownload
    testSingleFileDownload: typeof testSingleFileDownload
    analyzeDocumentFiles: typeof analyzeDocumentFiles
  }
}

// 自动注册到全局
if (typeof window !== 'undefined') {
  window.debugFileDownload = debugFileDownload
  window.testSingleFileDownload = testSingleFileDownload
  window.analyzeDocumentFiles = analyzeDocumentFiles
  
  console.error('🔧 调试函数已注册到全局:')
  console.error('  - window.debugFileDownload() - 完整调试流程')
  console.error('  - window.testSingleFileDownload(token) - 测试单个文件')
  console.error('  - window.analyzeDocumentFiles() - 分析文档文件类型')
}

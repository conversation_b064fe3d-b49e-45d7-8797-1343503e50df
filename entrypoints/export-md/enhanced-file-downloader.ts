import { Toast } from "@/pkg/lark/env"
import type * as mdast from 'mdast'
import {
  getJsSdkSession,
  getDocumentToken,
  getDocumentData,
  extractFileTokens,
  getFileCdnUrls,
  downloadAndDecryptFile,
  getAllDocumentFiles,
  isVideoFile,
  type FeishuFileInfo,
  type FeishuCdnResponse
} from '@/pkg/lark/file'

/**
 * 增强的文件下载结果
 */
export interface EnhancedDownloadResult {
  filename: string
  content: Blob
  token?: string
  downloadMethod: 'cdn' | 'fallback'
  isZip?: boolean
}

/**
 * 文件下载统计信息
 */
export interface DownloadStats {
  total: number
  cdnSuccess: number
  fallbackSuccess: number
  failed: number
  zipFiles: number
}

/**
 * 增强的文件下载器
 * 优先使用 CDN 方式下载，失败时回退到原有方式
 */
export class EnhancedFileDownloader {
  private jsSdkSession: string | null
  private stats: DownloadStats = {
    total: 0,
    cdnSuccess: 0,
    fallbackSuccess: 0,
    failed: 0,
    zipFiles: 0
  }

  constructor() {
    this.jsSdkSession = getJsSdkSession()
    console.error('🚀 增强文件下载器初始化', { hasSession: !!this.jsSdkSession })
  }

  /**
   * 检测文件是否为 ZIP 包
   */
  private isZipFile(filename: string): boolean {
    return /\.(zip|rar|7z|tar|gz|bz2)$/i.test(filename)
  }

  /**
   * 使用 CDN 方式下载文件
   */
  private async downloadViaCdn(file: mdast.Link): Promise<EnhancedDownloadResult | null> {
    if (!file.data?.name) return null

    console.error('🌐 尝试 CDN 方式下载:', file.data.name)

    try {
      // 获取文档数据和文件信息
      const token = getDocumentToken()
      const { docVar } = await getDocumentData(token, this.jsSdkSession)
      const fileTokens = extractFileTokens(docVar)

      console.error('🔍 查找匹配的文件 token，目标文件:', file.data.name)
      console.error('📁 可用的文件tokens:', fileTokens.length)

      // 查找匹配的文件 token
      // 由于我们无法直接从 mdast.Link 获取文件token，
      // 这里采用一种策略：如果只有少量文件，尝试下载所有文件
      let matchedToken: FeishuFileInfo | null = null

      if (fileTokens.length === 1) {
        // 如果只有一个文件，直接使用
        matchedToken = fileTokens[0]
        console.error('📎 只有一个文件，直接使用')
      } else if (fileTokens.length <= 5) {
        // 如果文件不多，可以尝试通过文件名匹配
        // 这里需要从文档数据中获取文件名信息
        const fileMap = docVar.data?.file_map || {}

        for (const [key, fileData] of Object.entries(fileMap)) {
          const fileInfo = (fileData as any).file
          if (fileInfo?.name === file.data.name) {
            matchedToken = fileTokens.find(ft => ft.file_token === fileInfo.token) || null
            if (matchedToken) {
              console.error('✅ 通过文件名匹配成功:', file.data.name)
              break
            }
          }
        }
      }

      if (!matchedToken) {
        console.error('❌ 未找到匹配的文件 token，尝试使用第一个文件token')
        // 作为最后的尝试，使用第一个可用的token
        matchedToken = fileTokens[0] || null
      }

      if (!matchedToken) {
        console.error('❌ 没有可用的文件 token')
        return null
      }

      console.error('🎯 使用文件token:', matchedToken.file_token.substring(0, 10) + '...')

      // 获取 CDN URL
      const cdnUrls = await getFileCdnUrls([matchedToken], this.jsSdkSession)
      if (cdnUrls.length === 0) {
        console.error('❌ 未获取到 CDN URL')
        return null
      }

      console.error('🔗 获取到 CDN URL:', cdnUrls[0].url.substring(0, 50) + '...')

      // 下载并解密文件
      const base64Data = await downloadAndDecryptFile(cdnUrls[0], this.jsSdkSession)
      if (!base64Data) {
        console.error('❌ CDN 下载失败')
        return null
      }

      // 转换为 Blob
      const binaryString = atob(base64Data)
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      const blob = new Blob([bytes])

      console.error('✅ CDN 下载成功:', file.data.name, '文件大小:', blob.size)
      this.stats.cdnSuccess++

      return {
        filename: `files/${file.data.name}`,
        content: blob,
        token: matchedToken.file_token,
        downloadMethod: 'cdn',
        isZip: this.isZipFile(file.data.name)
      }

    } catch (error) {
      console.error('❌ CDN 下载失败:', error)
      return null
    }
  }

  /**
   * 使用原有方式下载文件（回退方案）
   */
  private async downloadViaFallback(file: mdast.Link): Promise<EnhancedDownloadResult | null> {
    if (!file.data?.name || !file.data.fetchFile) return null

    console.error('🔄 使用回退方式下载:', file.data.name)

    try {
      const response = await file.data.fetchFile()

      if (!response.ok) {
        console.error('❌ 回退下载失败:', response.status, response.statusText)
        return null
      }

      const blob = await response.blob()

      console.error('✅ 回退下载成功:', file.data.name)
      this.stats.fallbackSuccess++

      return {
        filename: `files/${file.data.name}`,
        content: blob,
        downloadMethod: 'fallback',
        isZip: this.isZipFile(file.data.name)
      }

    } catch (error) {
      console.error('❌ 回退下载失败:', error)
      return null
    }
  }

  /**
   * 下载单个文件（智能选择下载方式）
   */
  async downloadFile(file: mdast.Link): Promise<EnhancedDownloadResult | null> {
    if (!file.data?.name) return null

    this.stats.total++
    const isZip = this.isZipFile(file.data.name)
    if (isZip) {
      this.stats.zipFiles++
      console.error('📦 检测到 ZIP 文件:', file.data.name)
    }

    // 优先尝试 CDN 方式（特别是对于 ZIP 文件）
    if (this.jsSdkSession || isZip) {
      const cdnResult = await this.downloadViaCdn(file)
      if (cdnResult) {
        return cdnResult
      }
    }

    // 回退到原有方式
    const fallbackResult = await this.downloadViaFallback(file)
    if (fallbackResult) {
      return fallbackResult
    }

    // 都失败了
    console.error('❌ 所有下载方式都失败:', file.data.name)
    this.stats.failed++
    return null
  }

  /**
   * 批量下载文件
   */
  async downloadFiles(
    files: mdast.Link[],
    options: {
      batchSize?: number
      onProgress?: (progress: number) => void
      onComplete?: () => void
      signal?: AbortSignal
    } = {}
  ): Promise<EnhancedDownloadResult[]> {
    const { batchSize = 3, onProgress, onComplete, signal } = options

    console.error('📁 开始批量下载文件，总数:', files.length)

    const results: EnhancedDownloadResult[] = []
    let completed = 0

    // 分批处理
    for (let i = 0; i < files.length; i += batchSize) {
      if (signal?.aborted) break

      const batch = files.slice(i, i + batchSize)
      const batchPromises = batch.map(async (file) => {
        const result = await this.downloadFile(file)
        completed++
        onProgress?.(completed / files.length)
        return result
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults.filter(r => r !== null) as EnhancedDownloadResult[])
    }

    onComplete?.()

    console.error('📊 下载统计:', this.stats)
    return results
  }

  /**
   * 获取下载统计信息
   */
  getStats(): DownloadStats {
    return { ...this.stats }
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      total: 0,
      cdnSuccess: 0,
      fallbackSuccess: 0,
      failed: 0,
      zipFiles: 0
    }
  }
}

/**
 * 创建增强文件下载器实例
 */
export const createEnhancedFileDownloader = (): EnhancedFileDownloader => {
  return new EnhancedFileDownloader()
}

/**
 * 快速获取所有文档文件（使用新的 CDN 方式）
 */
export const quickGetAllDocumentFiles = async (): Promise<Record<string, string>> => {
  try {
    Toast.loading({
      content: '正在使用增强方式获取文档文件...',
      keepAlive: true,
      key: 'enhanced_download'
    })

    const files = await getAllDocumentFiles()

    Toast.remove('enhanced_download')
    Toast.success({
      content: `成功获取 ${Object.keys(files).length} 个文件`
    })

    return files
  } catch (error) {
    Toast.remove('enhanced_download')
    Toast.error({
      content: `增强下载失败: ${error instanceof Error ? error.message : '未知错误'}`
    })
    throw error
  }
}

# 飞书文档增强文件下载功能

## 概述

基于飞书文档文件获取技术方案，实现了一套增强的文件下载系统，特别针对 ZIP 包等附件的下载进行了优化。该系统通过模拟飞书客户端行为，获取文档中所有文件的 CDN 下载链接，突破飞书的文件访问限制。

## 核心特性

### 1. 双重下载策略
- **CDN 优先**: 优先使用飞书 CDN 接口获取加密文件并解密
- **智能回退**: CDN 方式失败时自动回退到原有下载方式
- **ZIP 包优化**: 特别针对 ZIP 包等压缩文件进行优化

### 2. 完整的技术实现
- **会话认证**: 自动获取 `jssdk-session` 进行权限认证
- **文档解析**: 解析文档数据获取文件映射表
- **批量处理**: 支持批量获取 CDN 链接（每批最多 200 个文件）
- **文件解密**: 使用 AES-GCM 算法解密下载的文件

### 3. 详细的调试信息
- **分步日志**: 每个步骤都有详细的调试输出
- **统计信息**: 提供下载成功率、方式统计等信息
- **错误处理**: 完善的错误处理和用户提示

## 文件结构

```
entrypoints/export-md/
├── enhanced-file-downloader.ts    # 增强文件下载器核心实现
├── debug-file-download.ts         # 调试功能
├── test-enhanced-download.ts      # 测试功能
└── README-enhanced-download.md    # 本文档
```

## 核心 API

### 1. 基础文件操作 (`pkg/lark/file.ts`)

```typescript
// 获取 jssdk-session
const session = getJsSdkSession()

// 获取文档数据
const { docVar, token } = await getDocumentData(documentToken, session)

// 提取文件 tokens
const fileTokens = extractFileTokens(docVar)

// 获取 CDN 链接
const cdnUrls = await getFileCdnUrls(fileTokens, session)

// 下载并解密文件
const fileData = await downloadAndDecryptFile(cdnUrls[0], session)

// 一键获取所有文档文件
const allFiles = await getAllDocumentFiles()
```

### 2. 增强文件下载器

```typescript
import { createEnhancedFileDownloader } from './enhanced-file-downloader'

const downloader = createEnhancedFileDownloader()

// 下载单个文件
const result = await downloader.downloadFile(mdastLinkNode)

// 批量下载文件
const results = await downloader.downloadFiles(files, {
  batchSize: 3,
  onProgress: (progress) => console.log(`进度: ${progress * 100}%`),
  onComplete: () => console.log('下载完成')
})

// 获取统计信息
const stats = downloader.getStats()
```

## 调试和测试

### 1. 浏览器控制台调试

在飞书文档页面的浏览器控制台中，可以使用以下函数进行调试：

```javascript
// 完整调试流程
window.debugFileDownload()

// 测试单个文件下载
window.testSingleFileDownload('file_token_here')

// 分析文档文件类型
window.analyzeDocumentFiles()

// 测试增强下载功能
window.testEnhancedDownload()

// 快速分析文件（不下载）
window.quickAnalyzeFiles()
```

### 2. 调试输出说明

调试过程中会输出详细的日志信息，包括：

- 🔑 会话信息获取
- 📄 文档数据获取
- 🔍 文件 token 提取
- 🌐 CDN 链接获取
- 📥 文件下载过程
- 🔓 文件解密过程
- 📊 统计信息

## 技术原理

### 1. 权限认证机制

```typescript
// 从多个来源获取 jssdk-session
function getJsSdkSession(): string | null {
  // 1. 从页面脚本获取
  // 2. 从 cookie 获取  
  // 3. 从 localStorage 获取
}
```

### 2. 文档数据获取

```typescript
// 多接口尝试策略
async function getDocumentData(token: string, session?: string) {
  // 1. 尝试主接口: /wiki/{token}
  // 2. 尝试备用接口: /file/{token} -> /doc/{newToken}
}
```

### 3. CDN URL 获取

```typescript
// 批量获取 CDN 链接
const response = await fetch('/space/api/box/file/cdn_url/', {
  method: 'POST',
  headers: { 'jssdk-session': session },
  body: JSON.stringify(fileTokens)
})
```

### 4. 文件解密

```typescript
// AES-GCM 解密
async function decryptFile(key: string, iv: string, data: Uint8Array) {
  const cryptoKey = await crypto.subtle.importKey('raw', keyArray, { name: 'AES-GCM' })
  const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, cryptoKey, data)
  return new Uint8Array(decrypted)
}
```

## 使用场景

### 1. Markdown 导出增强
- 在 Markdown 导出时自动使用增强下载
- 特别优化 ZIP 包等大文件的下载
- 提供详细的下载进度和统计信息

### 2. 文档备份
- 批量下载文档中的所有文件
- 支持各种文件类型（图片、视频、ZIP 包等）
- 保持文件的完整性和原始格式

### 3. 内容迁移
- 从飞书文档迁移到其他平台
- 保留所有附件和媒体文件
- 支持大批量文件处理

## 注意事项

### 1. 权限要求
- 用户必须已登录飞书账号
- 必须有访问目标文档的权限
- `jssdk-session` 有时效性，需要定期更新

### 2. 限制和异常处理
- 单次请求文件数量限制：建议 200 个以内
- 网络请求可能失败：需要重试机制
- 文件可能已被删除或权限变更
- 解密可能失败：需要验证 key 和 iv 的有效性

### 3. 性能优化
- 使用批量处理减少网络请求
- 智能选择下载方式（CDN vs 原有方式）
- 提供进度回调和统计信息

### 4. 法律合规
- 仅用于有权限访问的文档
- 遵守相关法律法规
- 尊重知识产权

## 故障排除

### 1. 常见问题

**Q: 获取不到 jssdk-session**
A: 确保已登录飞书账号，刷新页面后重试

**Q: CDN 下载失败**
A: 系统会自动回退到原有下载方式，检查网络连接

**Q: 文件解密失败**
A: 检查文件的 key 和 iv 是否正确，可能是文件已损坏

**Q: ZIP 文件下载不完整**
A: 使用调试功能检查文件大小，确认下载过程是否正常

### 2. 调试步骤

1. 在控制台运行 `window.analyzeDocumentFiles()` 分析文档
2. 运行 `window.debugFileDownload()` 进行完整调试
3. 检查控制台输出的详细日志
4. 根据错误信息进行相应处理

## 更新日志

### v1.0.0 (2024-12-26)
- 实现基础的 CDN 文件下载功能
- 添加 AES-GCM 文件解密支持
- 集成到 Markdown 导出流程
- 提供完整的调试和测试工具
- 特别优化 ZIP 包下载功能

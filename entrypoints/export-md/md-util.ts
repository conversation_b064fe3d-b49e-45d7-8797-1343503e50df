import { Toast } from "@/pkg/lark/env"
import { Docx, docx } from '@/pkg/lark/docx'
import type * as mdast from 'mdast'
import { OneHundred, Second, waitFor } from '@/pkg/common'
import { fileSave, supported } from 'browser-fs-access'
import { fs } from '@zip.js/zip.js'
import normalizeFileName from 'filenamify/browser'
import { cluster } from 'radash'
import { confirmWithCancel } from '@/pkg/utils/notification'
import {
  createEnhancedFileDownloader
} from './enhanced-file-downloader'
import './debug-file-download' // 引入调试功能
import './test-enhanced-download' // 引入测试功能

const DOWNLOAD_ABORTED = 'Download aborted'

enum ToastKey {
  DOWNLOADING = 'downloading',
  REPORT_BUG = 'report_bug',
}

const usedNames = new Set<string>()
const fileNameToPreId = new Map<string, number>()

// 创建安全的文档前缀，去除特殊字符并限制长度
const createDocPrefix = (docTitle?: string): string => {
  if (!docTitle) return ''

  // 去除特殊字符，只保留字母、数字、中文字符和连字符
  const cleanTitle = docTitle
    .replace(/[^\w\u4e00-\u9fa5-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '')

  // 限制长度为10个字符
  const prefix = cleanTitle.slice(0, 10)
  return prefix ? `${prefix}_` : ''
}

const uniqueFileName = (originFileName: string, docPrefix?: string) => {
  // 使用文档前缀创建基础文件名
  const baseFileName = docPrefix ? `${docPrefix}${originFileName}` : originFileName

  if (usedNames.has(baseFileName)) {
    const startDotIndex = baseFileName.lastIndexOf('.')

    const preId = fileNameToPreId.get(baseFileName) ?? 0
    const id = preId + 1
    fileNameToPreId.set(baseFileName, id)

    const fileName =
      startDotIndex === -1
        ? baseFileName.concat(`-${id.toFixed()}`)
        : baseFileName
          .slice(0, startDotIndex)
          .concat(`-${id.toFixed()}`)
          .concat(baseFileName.slice(startDotIndex))

    return fileName
  }

  usedNames.add(baseFileName)
  return baseFileName
}

interface ProgressOptions {
  onProgress?: (progress: number) => void
  onComplete?: () => void
}

async function toBlob(
  response: Response,
  options: ProgressOptions = {},
): Promise<Blob> {
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status.toFixed()}`)
  }

  if (!response.body) {
    throw new Error('This request has no response body.')
  }

  const { onProgress, onComplete } = options

  const reader = response.body.getReader()
  const contentLength = parseInt(
    response.headers.get('Content-Length') ?? '0',
    10,
  )

  let receivedLength = 0
  const chunks = []

  let _done = false
  while (!_done) {
    const { done, value } = await reader.read()

    _done = done

    if (done) {
      onComplete?.()
      break
    }

    chunks.push(value)
    receivedLength += value.length

    onProgress?.(receivedLength / contentLength)
  }

  const blob = new Blob(chunks)
  return blob
}

const withSignal = async <T>(
  fn: (isAborted: () => boolean) => Promise<T>,
  options: {
    signal?: AbortSignal
    onAbort?: () => void
  } = {},
): Promise<T | null> => {
  const { signal, onAbort } = options

  let isAborted = false

  const checkAborted = () => {
    if (signal?.aborted || isAborted) {
      isAborted = true
      return true
    }
    return false
  }

  if (signal) {
    signal.addEventListener('abort', () => {
      isAborted = true
      onAbort?.()
    })
  }

  try {
    return await fn(checkAborted)
  } catch (error) {
    if (isAborted || (error instanceof DOMException && error.name === 'AbortError')) {
      onAbort?.()
      return null
    }
    throw error
  }
}

const downloadImage = async (
  image: mdast.Image,
  options: {
    signal?: AbortSignal
    docPrefix?: string
  } = {},
): Promise<DownloadResult | null> => {
  if (!image.data) return null

  const { signal, docPrefix } = options
  const { name: originName, fetchSources, fetchBlob } = image.data

  const result = await withSignal(
    async isAborted => {
      try {
        // whiteboard
        if (fetchBlob) {
          if (isAborted()) return null

          const content = await fetchBlob()
          if (!content) return null

          const name = uniqueFileName('diagram.png', docPrefix)
          const filename = `images/${name}`

          image.url = filename

          return {
            filename,
            content,
          }
        }

        // image
        if (originName && fetchSources) {
          if (isAborted()) return null

          const sources = await fetchSources()
          if (!sources) return null

          const name = uniqueFileName(originName, docPrefix)
          const filename = `images/${name}`

          const { src } = sources
          if (isAborted()) return null

          const response = await fetch(src, { signal })

          try {
            if (isAborted()) return null

            const blob = await toBlob(response, {
              onProgress: progress => {
                if (isAborted()) {
                  Toast.remove(filename)
                  return
                }

                Toast.loading({
                  content: `下载 ${name} 中：${Math.floor(progress * OneHundred)}%（请不要刷新或关闭页面）`,
                  keepAlive: true,
                  key: filename,
                })
              },
            })

            image.url = filename

            return {
              filename,
              content: blob,
            }
          } finally {
            Toast.remove(filename)
          }
        }

        return null
      } catch (error) {
        const isAbortError =
          isAborted() ||
          (error instanceof DOMException && error.name === 'AbortError')

        if (!isAbortError) {
          Toast.error({
            content: `下载 ${originName} 失败`,
            actionText: '确认',
          })
        }

        return null
      }
    },
    { signal },
  )

  return result
}

const downloadFile = async (
  file: mdast.Link,
  options: {
    signal?: AbortSignal
    docPrefix?: string
  } = {},
): Promise<DownloadResult | null> => {
  if (!file.data?.name || !file.data.fetchFile) return null

  const { signal, docPrefix } = options
  const { name, fetchFile } = file.data

  // 检测文件类型
  const isVideo = /\.(mp4|avi|mov|mkv|wmv|flv|webm|m4v|3gp|ogv)$/i.test(name)
  console.error(`📁 开始下载文件: ${name} (${isVideo ? '视频' : '普通文件'})`)

  let controller = new AbortController()

  const cancel = () => {
    controller.abort()
  }

  const result = await withSignal(
    async () => {
      try {
        const filename = `files/${uniqueFileName(name, docPrefix)}`

        console.error(`📁 获取文件响应: ${name}`)
        const response = await fetchFile({ signal: controller.signal })

        // 检查响应状态
        console.error(`📁 响应状态: ${response.status} ${response.statusText} for ${name}`)
        console.error(`📁 响应URL: ${response.url}`)
        console.error(`📁 响应headers:`, Object.fromEntries(response.headers.entries()))

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        try {
          const blob = await toBlob(response, {
            onProgress: progress => {
              const fileType = isVideo ? '视频' : '文件'
              Toast.loading({
                content: `下载 ${fileType} ${name} 中：${Math.floor(progress * OneHundred)}%（请不要刷新或关闭页面）`,
                keepAlive: true,
                key: filename,
                actionText: '取消',
                onActionClick: cancel,
              })
            },
          })

          console.error(`📁 文件下载完成: ${name}, 大小: ${blob.size} bytes`)
          file.url = filename

          return {
            filename,
            content: blob,
          }
        } finally {
          Toast.remove(filename)
        }
      } catch (error) {
        if (error instanceof DOMException && error.name === 'AbortError') {
          console.error(`📁 文件下载被取消: ${name}`)
          return null
        }

        console.error(`📁 文件下载失败: ${name}`, error)

        Toast.error({
          content: `下载 ${name} 失败: ${error instanceof Error ? error.message : '未知错误'}`,
          actionText: '确认',
        })

        return null
      }
    },
    { signal, onAbort: cancel },
  )

  // @ts-expect-error remove reference
  controller = null

  return result
}

interface DownloadResult {
  filename: string
  content: Blob
}

type File = mdast.Image | mdast.Link

const downloadFiles = async (
  files: File[],
  options: ProgressOptions & {
    batchSize?: number
    signal?: AbortSignal
    docPrefix?: string
    useEnhanced?: boolean
  } = {},
): Promise<DownloadResult[]> => {
  const { onProgress, onComplete, batchSize = 3, signal, docPrefix, useEnhanced = true } = options

  let completeEventCalled = false
  const onCompleteOnce = () => {
    if (!completeEventCalled) {
      completeEventCalled = true
      onComplete?.()
    }
  }

  // 如果启用增强下载且文件中包含链接类型
  const linkFiles = files.filter((file): file is mdast.Link => file.type === 'link')
  if (useEnhanced && linkFiles.length > 0) {
    console.error('🚀 使用增强文件下载器')

    try {
      const enhancedDownloader = createEnhancedFileDownloader()
      const enhancedResults = await enhancedDownloader.downloadFiles(linkFiles, {
        batchSize,
        onProgress,
        onComplete: onCompleteOnce,
        signal
      })

      // 转换为原有格式
      const results: DownloadResult[] = enhancedResults.map(result => ({
        filename: result.filename,
        content: result.content
      }))

      // 输出统计信息
      const stats = enhancedDownloader.getStats()
      console.error('📊 增强下载统计:', stats)

      if (stats.zipFiles > 0) {
        Toast.info({
          content: `成功下载 ${stats.zipFiles} 个 ZIP 文件，${stats.cdnSuccess} 个使用 CDN 方式`
        })
      }

      return results
    } catch (error) {
      console.error('❌ 增强下载失败，回退到原有方式:', error)
      Toast.warning({ content: '增强下载失败，使用原有方式下载' })
      // 继续使用原有方式
    }
  }

  const results = await withSignal(
    async isAborted => {
      const _results: DownloadResult[] = []

      const totalSize = files.length
      let downloadedSize = 0

      for (const batch of cluster(files, batchSize)) {
        if (isAborted()) break

        await Promise.allSettled(
          batch.map(async file => {
            if (isAborted()) return

            try {
              const result =
                file.type === 'image'
                  ? await downloadImage(file, { signal, docPrefix })
                  : await downloadFile(file, { signal, docPrefix })

              if (result) {
                _results.push(result)
              }
            } finally {
              downloadedSize++

              if (!isAborted()) {
                onProgress?.(downloadedSize / totalSize)
              }
            }
          }),
        )
      }

      onCompleteOnce()
      return _results
    },
    {
      signal,
      onAbort: onCompleteOnce,
    },
  )

  return results ?? []
}

interface PrepareResult {
  isReady: boolean
  recoverScrollTop?: () => void
}

const prepare = async (): Promise<PrepareResult> => {
  const checkIsReady = () => docx.isReady({ checkWhiteboard: true })

  let recoverScrollTop

  if (!checkIsReady()) {
    const initialScrollTop = docx.container?.scrollTop ?? 0
    recoverScrollTop = () => {
      docx.scrollTo({
        top: initialScrollTop,
        behavior: 'instant',
      })
    }

    let top = 0

    docx.scrollTo({
      top,
      behavior: 'instant',
    })

    const maxTryTimes = OneHundred
    let tryTimes = 0

    Toast.loading({
      content: '滚动中，以便加载文档',
      keepAlive: true,
      key: 'scroll_document',
      actionText: '取消',
      onActionClick: () => {
        tryTimes = maxTryTimes
      },
    })

    while (!checkIsReady() && tryTimes <= maxTryTimes) {
      docx.scrollTo({
        top,
        behavior: 'smooth',
      })

      await waitFor(0.4 * Second)

      tryTimes++
      top = docx.container?.scrollHeight ?? 0
    }

    Toast.remove('scroll_document')
  }

  return {
    isReady: checkIsReady(),
    recoverScrollTop,
  }
}


const main = async (options: { signal?: AbortSignal } = {}) => {
  const { signal } = options

  if (!docx.rootBlock) {
    Toast.warning({ content: '当前文档无法导出' })
    throw new Error(DOWNLOAD_ABORTED)
  }

  const { isReady, recoverScrollTop } = await prepare()

  if (!isReady) {
    Toast.warning({
      content: '部分内容仍在加载中，暂时无法下载。请等待加载完成后重试',
    })
    throw new Error(DOWNLOAD_ABORTED)
  }

  const { root, images, files } = docx.intoMarkdownAST({
    whiteboard: true,
    file: true,
  })

  const recommendName = docx.pageTitle
    ? normalizeFileName(docx.pageTitle.slice(0, OneHundred))
    : 'doc'
  const isZip = images.length > 0 || files.length > 0
  const ext = isZip ? '.zip' : '.md'
  const filename = `${recommendName}${ext}`

  const toBlobContent = async () => {
    Toast.loading({
      content: '正在准备文档内容（请不要刷新或关闭页面）',
      keepAlive: true,
      key: ToastKey.DOWNLOADING,
    })

    const singleFileContent = () => {
      const markdown = Docx.stringify(root)
      return new Blob([markdown])
    }

    const zipFileContent = async () => {
      const zipFs = new fs.FS()

      // 创建文档前缀
      const docPrefix = createDocPrefix(docx.pageTitle)

      const imgs = images.filter(image => image.data?.fetchSources)
      const diagrams = images.filter(image => image.data?.fetchBlob)

      Toast.loading({
        content: '正在下载图片和文件（请不要刷新或关闭页面）',
        keepAlive: true,
        key: ToastKey.DOWNLOADING,
      })

      const results = await Promise.all([
        downloadFiles(imgs, {
          batchSize: 15,
          onProgress: progress => {
            Toast.loading({
              content: `图片下载进度：${Math.floor(progress * OneHundred)}%`,
              keepAlive: true,
              key: 'image_progress',
            })
          },
          onComplete: () => {
            Toast.remove('image_progress')
          },
          signal,
          docPrefix,
        }),
        // Diagrams must be downloaded one by one
        downloadFiles(diagrams, {
          batchSize: 1,
          signal,
          docPrefix,
        }),
        downloadFiles(files, {
          onProgress: progress => {
            Toast.loading({
              content: `文件下载进度：${Math.floor(progress * OneHundred)}%`,
              keepAlive: true,
              key: 'file_progress',
            })
          },
          onComplete: () => {
            Toast.remove('file_progress')
          },
          signal,
          docPrefix,
          useEnhanced: true, // 启用增强下载
        }),
      ])

      Toast.loading({
        content: '正在打包文件（请不要刷新或关闭页面）',
        keepAlive: true,
        key: ToastKey.DOWNLOADING,
      })

      results.flat(1).forEach(({ filename, content }) => {
        zipFs.addBlob(filename, content)
      })

      const markdown = Docx.stringify(root)
      zipFs.addText(`${recommendName}.md`, markdown)

      return await zipFs.exportBlob()
    }

    const content = isZip ? await zipFileContent() : singleFileContent()

    Toast.loading({
      content: '内容准备完成，即将保存文件...',
      keepAlive: true,
      key: ToastKey.DOWNLOADING,
    })

    recoverScrollTop?.()
    return content
  }

  if (!supported) {
    Toast.error({ content: '当前浏览器不支持文件保存功能，请使用现代浏览器' })
    throw new Error(DOWNLOAD_ABORTED)
  }

  try {
    // 先下载所有内容
    console.error('📁 开始下载所有内容...')
    const blob = await toBlobContent()
    console.error('📁 所有内容下载完成，准备保存文件')

    // 检查用户激活状态，如果失效则重新激活
    if (!navigator.userActivation?.isActive) {
      console.error('📁 用户激活状态失效，需要重新激活')
      // 用户激活状态失效时，先获取用户确认来重新激活
      const confirmed = await confirmWithCancel('文件已准备完成，是否保存Markdown文件？')
      if (!confirmed) {
        throw new Error(DOWNLOAD_ABORTED)
      }
    }

    console.error('📁 开始保存文件...')
    await fileSave(blob, {
      fileName: filename,
      extensions: [ext],
    })
    console.error('📁 文件保存成功')
  } catch (error) {
    console.error('📁 文件保存失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'

    if (errorMessage.includes('user gesture') || errorMessage.includes('User activation')) {
      Toast.error({
        content: '文件保存需要用户操作，请重新点击导出按钮'
      })
    } else {
      Toast.error({
        content: `文件保存失败: ${errorMessage}`
      })
    }
    throw error
  }
}

export const exportMdInjected = async () => {
  let controller = new AbortController()

  try {
    await main({
      signal: controller.signal,
    })

    Toast.success({
      content: '下载完成',
    })
  } catch (error: unknown) {
    const aborted =
      error instanceof Error &&
      (error.name === 'AbortError' || error.message === DOWNLOAD_ABORTED)

    if (aborted) {
      controller.abort()
    } else {
      Toast.error({
        key: ToastKey.REPORT_BUG,
        content: `下载过程中出现错误: ${String(error)}`,
        actionText: '确认',
      })
    }
  } finally {
    Toast.remove(ToastKey.DOWNLOADING)
    // @ts-expect-error remove reference
    controller = null
  }
}
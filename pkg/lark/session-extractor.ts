/**
 * 飞书 jssdk-session 提取工具
 * 提供多种策略来获取页面中的 session 信息
 */

export interface SessionExtractionResult {
  success: boolean
  session: string | null
  method: string
  details?: any
}

/**
 * 高级 session 提取器
 */
export class SessionExtractor {
  private debug: boolean = true

  constructor(debug: boolean = true) {
    this.debug = debug
  }

  private log(...args: any[]): void {
    if (this.debug) {
      console.error(...args)
    }
  }

  /**
   * 方法1: 从页面脚本中提取 - 增强版
   */
  extractFromScripts(): SessionExtractionResult {
    this.log('🔍 方法1: 从页面脚本中提取...')
    
    try {
      const scripts = Array.from(document.scripts)
        .map(s => s.innerHTML)
        .filter(content => content && content.length > 0)

      this.log('📄 找到', scripts.length, '个有内容的脚本')

      // 扩展的正则表达式模式
      const patterns = [
        // 标准模式
        { name: '__jssdkSession__', regex: /__jssdkSession__\s*=\s*['"]([^'"]+)['"]/ },
        { name: 'jssdkSession', regex: /jssdkSession\s*[:=]\s*['"]([^'"]+)['"]/ },
        { name: 'jssdk-session', regex: /['"]jssdk-session['"]\s*:\s*['"]([^'"]+)['"]/ },
        
        // 变体模式
        { name: 'window.__jssdkSession__', regex: /window\.__jssdkSession__\s*=\s*['"]([^'"]+)['"]/ },
        { name: 'window.jssdkSession', regex: /window\.jssdkSession\s*=\s*['"]([^'"]+)['"]/ },
        { name: '__JSSDK_SESSION__', regex: /__JSSDK_SESSION__\s*=\s*['"]([^'"]+)['"]/ },
        
        // JSON 格式
        { name: 'JSON jssdkSession', regex: /"jssdkSession"\s*:\s*"([^"]+)"/ },
        { name: 'JSON jssdk-session', regex: /"jssdk-session"\s*:\s*"([^"]+)"/ },
        
        // 其他可能的格式
        { name: 'session token', regex: /session['"]\s*:\s*['"]([^'"]{20,})['"]/ },
        { name: 'token', regex: /token['"]\s*:\s*['"]([^'"]{30,})['"]/ }
      ]

      for (let i = 0; i < scripts.length; i++) {
        const script = scripts[i]
        
        // 检查是否包含可能的session相关内容
        if (script.includes('session') || script.includes('Session') || script.includes('jssdk') || script.includes('token')) {
          this.log(`🔍 检查脚本 ${i + 1}/${scripts.length} (长度: ${script.length})`)
          
          for (const pattern of patterns) {
            const match = script.match(pattern.regex)
            if (match && match[1] && match[1].length > 10) {
              this.log('✅ 找到session:', pattern.name, '长度:', match[1].length)
              return {
                success: true,
                session: match[1],
                method: `script-${pattern.name}`,
                details: { scriptIndex: i, patternName: pattern.name }
              }
            }
          }
        }
      }

      // 如果没找到，尝试查找任何长字符串
      this.log('🔍 查找可能的token字符串...')
      for (let i = 0; i < scripts.length; i++) {
        const script = scripts[i]
        const tokenPattern = /['"]([a-zA-Z0-9_-]{30,})['"]/g
        let match
        while ((match = tokenPattern.exec(script)) !== null) {
          const token = match[1]
          if (token.includes('_') || token.includes('-')) {
            this.log('🤔 发现可能的token:', token.substring(0, 20) + '...', '长度:', token.length)
          }
        }
      }

      return { success: false, session: null, method: 'script' }
    } catch (error) {
      this.log('❌ 脚本提取失败:', error)
      return { success: false, session: null, method: 'script', details: { error } }
    }
  }

  /**
   * 方法2: 从 Cookie 中提取 - 增强版
   */
  extractFromCookies(): SessionExtractionResult {
    this.log('🔍 方法2: 从 Cookie 中提取...')
    
    try {
      const cookies = document.cookie.split(';')
      this.log('🍪 检查', cookies.length, '个cookie')

      const cookieNames = [
        'jssdk-session',
        'jssdkSession',
        'jssdk_session',
        'session',
        'Session',
        '_session',
        'feishu_session',
        'lark_session',
        'auth_session',
        'user_session'
      ]

      for (const cookie of cookies) {
        const trimmed = cookie.trim()
        if (!trimmed) continue

        for (const cookieName of cookieNames) {
          if (trimmed.startsWith(cookieName + '=')) {
            const sessionValue = trimmed.split('=')[1]?.trim()
            if (sessionValue && sessionValue.length > 10) {
              this.log('✅ 从cookie找到session:', cookieName, '长度:', sessionValue.length)
              return {
                success: true,
                session: sessionValue,
                method: `cookie-${cookieName}`,
                details: { cookieName }
              }
            }
          }
        }
      }

      return { success: false, session: null, method: 'cookie' }
    } catch (error) {
      this.log('❌ Cookie提取失败:', error)
      return { success: false, session: null, method: 'cookie', details: { error } }
    }
  }

  /**
   * 方法3: 从存储中提取
   */
  extractFromStorage(): SessionExtractionResult {
    this.log('🔍 方法3: 从存储中提取...')
    
    const storageKeys = [
      'jssdk-session',
      'jssdkSession',
      'jssdk_session',
      'session',
      'Session',
      '_session',
      'feishu_session',
      'lark_session',
      'auth_session',
      'user_session'
    ]

    // 检查 localStorage
    try {
      for (const key of storageKeys) {
        const value = localStorage.getItem(key)
        if (value && value.length > 10) {
          this.log('✅ 从localStorage找到session:', key, '长度:', value.length)
          return {
            success: true,
            session: value,
            method: `localStorage-${key}`,
            details: { storageType: 'localStorage', key }
          }
        }
      }
    } catch (error) {
      this.log('❌ localStorage检查失败:', error)
    }

    // 检查 sessionStorage
    try {
      for (const key of storageKeys) {
        const value = sessionStorage.getItem(key)
        if (value && value.length > 10) {
          this.log('✅ 从sessionStorage找到session:', key, '长度:', value.length)
          return {
            success: true,
            session: value,
            method: `sessionStorage-${key}`,
            details: { storageType: 'sessionStorage', key }
          }
        }
      }
    } catch (error) {
      this.log('❌ sessionStorage检查失败:', error)
    }

    return { success: false, session: null, method: 'storage' }
  }

  /**
   * 方法4: 从全局变量中提取
   */
  extractFromGlobals(): SessionExtractionResult {
    this.log('🔍 方法4: 从全局变量中提取...')
    
    const globalPaths = [
      'window.__jssdkSession__',
      'window.jssdkSession',
      'window.jssdk_session',
      'window.session',
      'window.Session',
      'window._session',
      'window.feishu_session',
      'window.lark_session'
    ]

    for (const path of globalPaths) {
      try {
        const value = eval(path)
        if (value && typeof value === 'string' && value.length > 10) {
          this.log('✅ 从全局变量找到session:', path, '长度:', value.length)
          return {
            success: true,
            session: value,
            method: `global-${path}`,
            details: { globalPath: path }
          }
        }
      } catch (e) {
        // 忽略eval错误
      }
    }

    return { success: false, session: null, method: 'global' }
  }

  /**
   * 方法5: 从网络请求中提取（检查已发送的请求）
   */
  extractFromNetworkRequests(): SessionExtractionResult {
    this.log('🔍 方法5: 从网络请求中提取...')
    
    // 这个方法需要在开发者工具中手动检查
    // 或者通过拦截fetch/XMLHttpRequest来实现
    this.log('💡 提示: 请在开发者工具的Network标签页中查找包含jssdk-session的请求')
    
    return { success: false, session: null, method: 'network' }
  }

  /**
   * 综合提取方法 - 尝试所有策略
   */
  extractSession(): SessionExtractionResult {
    this.log('🚀 开始综合session提取...')
    
    const methods = [
      () => this.extractFromScripts(),
      () => this.extractFromCookies(),
      () => this.extractFromStorage(),
      () => this.extractFromGlobals(),
      () => this.extractFromNetworkRequests()
    ]

    for (const method of methods) {
      const result = method()
      if (result.success && result.session) {
        this.log('🎉 成功提取session:', result.method)
        return result
      }
    }

    this.log('❌ 所有方法都未能提取到session')
    return { success: false, session: null, method: 'all-failed' }
  }
}

/**
 * 快速获取session的便捷函数
 */
export const quickExtractSession = (debug: boolean = true): string | null => {
  const extractor = new SessionExtractor(debug)
  const result = extractor.extractSession()
  return result.session
}

/**
 * 详细的session提取分析
 */
export const analyzeSessionExtraction = (): SessionExtractionResult => {
  const extractor = new SessionExtractor(true)
  return extractor.extractSession()
}

const BASE_PATHNAME = '/api/box/stream/download/all/'
const VIDEO_PATHNAME = '/api/box/stream/download/video/'

/**
 * @description Resolve file download link.
 */
export const resolveFileDownloadUrl = ({
  token,
  recordId,
}: {
  token: string
  recordId: string
}): string => {
  const pathname = `/space${BASE_PATHNAME}${token}`
  const hostname = window.globalConfig?.drive_api?.[0]

  if (!hostname) {
    throw new Error('Failed to resolve file download url')
  }

  const url = new URL('https://' + hostname + pathname)
  url.searchParams.set('mount_node_token', recordId)
  url.searchParams.set('mount_point', 'docx_file')
  url.searchParams.set(
    'synced_block_host_token',
    window.location.pathname.split('/').at(-1) ?? '',
  )
  url.searchParams.set('synced_block_host_type', '22')
  return url.toString()
}

/**
 * 飞书文档文件信息接口
 */
export interface FeishuFileInfo {
  file_token: string
  platform: string
  type: string
}

/**
 * CDN URL 响应接口
 */
export interface FeishuCdnResponse {
  code: number
  data: Array<{
    file_token: string
    url: string
    key: string
    iv: string
  }>
}

import { quickExtractSession } from './session-extractor'

/**
 * 获取 jssdk-session (增强版)
 */
export const getJsSdkSession = (): string | null => {
  // 首先尝试使用增强的session提取器
  const enhancedSession = quickExtractSession(true)
  if (enhancedSession) {
    return enhancedSession
  }

  // 如果增强提取器失败，回退到原有方法
  console.error('🔑 开始获取 jssdk-session...')

  // 方法1: 从页面脚本中获取 - 多种模式匹配
  try {
    const scripts = Array.from(document.scripts)
      .map(s => s.innerHTML)
      .filter(content => content && content.length > 0)

    console.error('📄 找到', scripts.length, '个脚本标签')

    // 尝试多种可能的变量名和格式
    const patterns = [
      /__jssdkSession__\s*=\s*['"]([^'"]+)['"]/,
      /jssdkSession\s*[:=]\s*['"]([^'"]+)['"]/,
      /"jssdkSession"\s*:\s*"([^"]+)"/,
      /jssdk-session['"]\s*:\s*['"]([^'"]+)['"]/,
      /session['"]\s*:\s*['"]([^'"]+)['"]/,
      /__JSSDK_SESSION__\s*=\s*['"]([^'"]+)['"]/,
      /window\.__jssdkSession__\s*=\s*['"]([^'"]+)['"]/,
      /window\.jssdkSession\s*=\s*['"]([^'"]+)['"]/
    ]

    for (const script of scripts) {
      // 检查是否包含可能的session相关内容
      if (script.includes('session') || script.includes('Session') || script.includes('jssdk')) {
        console.error('🔍 检查脚本片段:', script.substring(0, 200) + '...')

        for (const pattern of patterns) {
          const match = script.match(pattern)
          if (match && match[1]) {
            console.error('✅ 从页面脚本获取到 jssdk-session (模式:', pattern.source, ')')
            return match[1]
          }
        }
      }
    }

    // 如果没找到，尝试查找任何看起来像session token的字符串
    for (const script of scripts) {
      // 查找长度合适的字符串，可能是session token
      const tokenPattern = /['"]([a-zA-Z0-9_-]{20,})['"]/g
      let match
      while ((match = tokenPattern.exec(script)) !== null) {
        const token = match[1]
        // 检查是否看起来像session token（包含特定字符模式）
        if (token.length > 30 && (token.includes('_') || token.includes('-'))) {
          console.error('🤔 发现可能的token:', token.substring(0, 20) + '...')
        }
      }
    }

  } catch (error) {
    console.error('❌ 从页面脚本获取 jssdk-session 失败:', error)
  }

  // 方法2: 从 cookie 获取 - 扩展搜索
  try {
    const cookies = document.cookie.split(';')
    console.error('🍪 检查', cookies.length, '个cookie')

    const sessionCookieNames = [
      'jssdk-session',
      'jssdkSession',
      'jssdk_session',
      'session',
      'Session',
      '_session',
      'feishu_session',
      'lark_session'
    ]

    for (const cookie of cookies) {
      const trimmedCookie = cookie.trim()
      console.error('🔍 检查cookie:', trimmedCookie.substring(0, 50) + '...')

      for (const cookieName of sessionCookieNames) {
        if (trimmedCookie.startsWith(cookieName + '=')) {
          const sessionValue = trimmedCookie.split('=')[1]?.trim()
          if (sessionValue && sessionValue.length > 10) {
            console.error('✅ 从 cookie 获取到 jssdk-session (名称:', cookieName, ')')
            return sessionValue
          }
        }
      }
    }
  } catch (error) {
    console.error('❌ 从 cookie 获取 jssdk-session 失败:', error)
  }

  // 方法3: 从 localStorage 获取 - 扩展搜索
  try {
    const sessionKeys = [
      'jssdk-session',
      'jssdkSession',
      'jssdk_session',
      'session',
      'Session',
      '_session',
      'feishu_session',
      'lark_session'
    ]

    console.error('💾 检查 localStorage...')

    for (const key of sessionKeys) {
      const sessionValue = localStorage.getItem(key)
      if (sessionValue && sessionValue.length > 10) {
        console.error('✅ 从 localStorage 获取到 jssdk-session (键名:', key, ')')
        return sessionValue
      }
    }

    // 遍历所有localStorage项目查找可能的session
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.includes('session') || key.includes('Session') || key.includes('jssdk'))) {
        const value = localStorage.getItem(key)
        if (value && value.length > 20) {
          console.error('🤔 发现可能的session存储:', key, '=', value.substring(0, 20) + '...')
        }
      }
    }
  } catch (error) {
    console.error('❌ 从 localStorage 获取 jssdk-session 失败:', error)
  }

  // 方法4: 从 sessionStorage 获取
  try {
    const sessionKeys = [
      'jssdk-session',
      'jssdkSession',
      'jssdk_session',
      'session',
      'Session'
    ]

    console.error('🗂️ 检查 sessionStorage...')

    for (const key of sessionKeys) {
      const sessionValue = sessionStorage.getItem(key)
      if (sessionValue && sessionValue.length > 10) {
        console.error('✅ 从 sessionStorage 获取到 jssdk-session (键名:', key, ')')
        return sessionValue
      }
    }
  } catch (error) {
    console.error('❌ 从 sessionStorage 获取 jssdk-session 失败:', error)
  }

  // 方法5: 从全局变量获取
  try {
    console.error('🌐 检查全局变量...')

    const globalVars = [
      'window.__jssdkSession__',
      'window.jssdkSession',
      'window.jssdk_session',
      'window.session',
      'window.Session'
    ]

    for (const varPath of globalVars) {
      try {
        const value = eval(varPath)
        if (value && typeof value === 'string' && value.length > 10) {
          console.error('✅ 从全局变量获取到 jssdk-session (变量:', varPath, ')')
          return value
        }
      } catch (e) {
        // 忽略eval错误
      }
    }
  } catch (error) {
    console.error('❌ 从全局变量获取 jssdk-session 失败:', error)
  }

  console.error('⚠️ 所有方法都未能获取到 jssdk-session')
  console.error('💡 建议：')
  console.error('  1. 确保已登录飞书账号')
  console.error('  2. 刷新页面后重试')
  console.error('  3. 检查是否在正确的飞书文档页面')
  console.error('  4. 查看上面的调试信息，寻找可能的session token')

  return null
}

/**
 * 获取文档 token
 */
export const getDocumentToken = (): string => {
  const url = window.location.href
  const pathParts = url.split('/').filter(Boolean)
  const token = pathParts[pathParts.length - 1]
  console.error('📄 文档 token:', token)
  return token
}

/**
 * @description Resolve video file download link (for video files like mp4, avi, etc.)
 */
export const resolveVideoDownloadUrl = ({
  token,
  recordId,
}: {
  token: string
  recordId: string
}): string => {
  const pathname = `/space${VIDEO_PATHNAME}${token}/`
  const hostname = window.globalConfig?.drive_api?.[0]

  if (!hostname) {
    throw new Error('Failed to resolve video download url')
  }

  const url = new URL('https://' + hostname + pathname)
  url.searchParams.set('mount_node_token', recordId)
  url.searchParams.set('mount_point', 'docx_file')
  url.searchParams.set('quality', '1080p')
  url.searchParams.set(
    'synced_block_host_token',
    window.location.pathname.split('/').at(-1) ?? '',
  )
  url.searchParams.set('synced_block_host_type', '22')
  return url.toString()
}

/**
 * @description Check if a file is a video file based on extension
 */
export const isVideoFile = (fileName: string): boolean => {
  return /\.(mp4|avi|mov|mkv|wmv|flv|webm|m4v|3gp|ogv)$/i.test(fileName)
}

/**
 * 获取文档数据
 */
export const getDocumentData = async (
  token: string,
  jsSdkSession?: string | null
): Promise<{ docVar: any; token: string }> => {
  console.error('📄 开始获取文档数据, token:', token)

  const headers: Record<string, string> = {}
  if (jsSdkSession) {
    headers['jssdk-session'] = jsSdkSession
    console.error('🔑 使用 jssdk-session 认证')
  }

  try {
    // 尝试主接口
    let url = `${location.protocol}//${location.host}/wiki/${token}`
    console.error('🌐 尝试主接口:', url)

    let response = await fetch(url, { headers })
    let data = await response.json()

    console.error('📡 主接口响应:', { code: data.code, message: data.message })

    if (data.code === 0) {
      console.error('✅ 主接口成功获取文档数据')
      return { docVar: data, token: token }
    }

    // 如果失败，尝试备用接口
    if (data.code === 4000003) {
      console.error('🔄 主接口失败，尝试备用接口...')

      url = `${location.protocol}//${location.host}/file/${token}`
      console.error('🌐 尝试备用接口1:', url)

      response = await fetch(url, { headers })
      data = await response.json()

      console.error('📡 备用接口1响应:', { code: data.code, message: data.message })

      if (data.code !== 0) {
        throw new Error(`备用接口1失败: ${data.message || '未知错误'}`)
      }

      const newToken = data.data?.token
      if (!newToken) {
        throw new Error('无法从备用接口1获取新token')
      }

      console.error('🔑 获取到新token:', newToken)

      // 使用新token重新请求
      url = `${location.protocol}//${location.host}/doc/${newToken}`
      console.error('🌐 使用新token请求:', url)

      response = await fetch(url, { headers })
      const finalData = await response.json()

      console.error('📡 最终接口响应:', { code: finalData.code, message: finalData.message })

      if (finalData.code !== 0) {
        throw new Error(`最终接口失败: ${finalData.message || '未知错误'}`)
      }

      console.error('✅ 备用接口成功获取文档数据')
      return { docVar: finalData, token: newToken }
    }

    throw new Error(`文档访问被拒绝: ${data.message || '未知错误'}`)
  } catch (error) {
    console.error('❌ 获取文档数据失败:', error)
    throw new Error(`获取文档数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 从文档数据中提取文件 tokens
 */
export const extractFileTokens = (docData: any): FeishuFileInfo[] => {
  console.error('🔍 开始提取文件 tokens...')

  const fileTokens: FeishuFileInfo[] = []
  const fileMap = docData.data?.file_map || {}

  console.error('📁 文件映射表大小:', Object.keys(fileMap).length)

  Object.entries(fileMap).forEach(([key, file]: [string, any]) => {
    console.error('🔍 检查文件:', { key, type: file.type, file })

    if (file.type === "image" || file.type === "file") {
      const fileInfo = file.file

      if (!fileInfo?.token) {
        console.error('⚠️ 文件缺少 token:', fileInfo)
        return
      }

      // 判断是否需要缩略图（仅对图片）
      const needsThumbnail = file.type === "image" &&
        fileInfo.height > 1280 &&
        (1280 * fileInfo.width / fileInfo.height) < 730

      const fileTokenInfo: FeishuFileInfo = {
        file_token: fileInfo.token,
        platform: "pc",
        type: needsThumbnail ? "" : "preview" // 空字符串表示原图，preview表示缩略图
      }

      fileTokens.push(fileTokenInfo)

      console.error('📎 添加文件token:', {
        token: fileInfo.token,
        name: fileInfo.name,
        type: file.type,
        needsThumbnail,
        finalType: fileTokenInfo.type
      })
    }
  })

  console.error(`✅ 提取完成，共找到 ${fileTokens.length} 个文件`)
  return fileTokens
}

/**
 * 批量获取文件 CDN 链接
 */
export const getFileCdnUrls = async (
  fileTokens: FeishuFileInfo[],
  jsSdkSession?: string | null
): Promise<FeishuCdnResponse['data']> => {
  console.error('🌐 开始批量获取文件 CDN 链接...')
  console.error('📊 文件数量:', fileTokens.length)

  const results: FeishuCdnResponse['data'] = []
  const batchSize = 200 // 每批处理200个文件

  // 准备请求头
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  }
  if (jsSdkSession) {
    headers["jssdk-session"] = jsSdkSession
    console.error('🔑 使用 jssdk-session 认证')
  }

  // 分批处理
  for (let i = 0; i < fileTokens.length; i += batchSize) {
    const batch = fileTokens.slice(i, i + batchSize)
    const batchNumber = Math.floor(i / batchSize) + 1
    const totalBatches = Math.ceil(fileTokens.length / batchSize)

    console.error(`📦 处理第 ${batchNumber}/${totalBatches} 批，包含 ${batch.length} 个文件`)

    try {
      const response = await fetch(
        `https://${location.host}/space/api/box/file/cdn_url/`,
        {
          method: "POST",
          headers: headers,
          body: JSON.stringify(batch)
        }
      )

      console.error(`📡 第 ${batchNumber} 批响应状态:`, response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: FeishuCdnResponse = await response.json()

      console.error(`📊 第 ${batchNumber} 批响应数据:`, {
        code: data.code,
        dataLength: data.data?.length || 0
      })

      // 验证响应格式
      if (data.code !== 0 || !Array.isArray(data.data)) {
        throw new Error(`第${batchNumber}批响应格式不正确: code=${data.code}`)
      }

      results.push(...data.data)
      console.error(`✅ 第 ${batchNumber} 批处理成功，获取到 ${data.data.length} 个文件链接`)

    } catch (error) {
      console.error(`❌ 第${batchNumber}批请求失败:`, error)
      // 可以选择继续处理其他批次或者抛出错误
      // 这里选择继续处理，但记录错误
    }
  }

  console.error(`🎉 批量获取完成，总共获取到 ${results.length} 个文件的 CDN 链接`)
  return results
}

/**
 * Base64 转 Uint8Array
 */
const base64ToUint8Array = (base64: string): Uint8Array => {
  const binaryString = atob(base64)
  const buffer = new ArrayBuffer(binaryString.length)
  const bytes = new Uint8Array(buffer)
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  return bytes
}

/**
 * ArrayBuffer 转 Base64
 */
const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
  let binary = ''
  const bytes = new Uint8Array(buffer)
  for (let i = 0; i < bytes.byteLength; i += 32768) {
    const chunk = bytes.subarray(i, Math.min(i + 32768, bytes.byteLength))
    binary += String.fromCharCode(...chunk)
  }
  return btoa(binary)
}

/**
 * 解密文件
 */
export const decryptFile = async (
  key: string,
  iv: string,
  encryptedData: Uint8Array
): Promise<Uint8Array> => {
  console.error('🔓 开始解密文件...')

  try {
    // Base64解码
    const keyArray = base64ToUint8Array(key)
    const ivArray = base64ToUint8Array(iv)

    console.error('🔑 密钥和IV长度:', { keyLength: keyArray.length, ivLength: ivArray.length })

    // 分离数据和认证标签
    const dataLength = encryptedData.length - 16
    const data = encryptedData.slice(0, dataLength)
    const authTag = encryptedData.slice(dataLength)

    console.error('📊 数据分离:', {
      totalLength: encryptedData.length,
      dataLength: data.length,
      authTagLength: authTag.length
    })

    // 导入密钥
    const cryptoKey = await crypto.subtle.importKey(
      "raw",
      keyArray.buffer as ArrayBuffer,
      { name: "AES-GCM" },
      false,
      ["decrypt"]
    )

    console.error('🔑 密钥导入成功')

    // 组合数据和标签
    const combined = new Uint8Array(data.length + authTag.length)
    combined.set(data, 0)
    combined.set(authTag, data.length)

    // 解密
    const decrypted = await crypto.subtle.decrypt(
      {
        name: "AES-GCM",
        iv: ivArray.buffer as ArrayBuffer,
        tagLength: 128
      },
      cryptoKey,
      combined.buffer as ArrayBuffer
    )

    console.error('✅ 文件解密成功，解密后大小:', decrypted.byteLength)
    return new Uint8Array(decrypted)

  } catch (error) {
    console.error('❌ 文件解密失败:', error)
    throw error
  }
}

/**
 * 下载并解密单个文件
 */
export const downloadAndDecryptFile = async (
  fileInfo: FeishuCdnResponse['data'][0],
  jsSdkSession?: string | null
): Promise<string | null> => {
  console.error('📥 开始下载文件:', fileInfo.file_token)

  try {
    const headers: Record<string, string> = {}
    if (jsSdkSession) {
      headers['jssdk-session'] = jsSdkSession
    }

    console.error('🌐 下载URL:', fileInfo.url)

    // 下载加密文件
    const response = await fetch(fileInfo.url, { headers })
    if (!response.ok) {
      console.error('❌ 文件下载失败:', response.status, response.statusText)
      return null
    }

    const encryptedData = await response.arrayBuffer()
    const encryptedArray = new Uint8Array(encryptedData)

    console.error('📊 下载完成，文件大小:', encryptedArray.length)

    // 解密文件
    const decryptedData = await decryptFile(
      fileInfo.key,
      fileInfo.iv,
      encryptedArray
    )

    // 转换为Base64（用于传输或存储）
    const base64Data = arrayBufferToBase64(decryptedData.buffer as ArrayBuffer)

    console.error('✅ 文件处理完成:', fileInfo.file_token)
    return base64Data

  } catch (error) {
    console.error('❌ 下载文件失败:', error)
    return null
  }
}

/**
 * 获取所有文档文件的完整流程
 */
export const getAllDocumentFiles = async (): Promise<Record<string, string>> => {
  console.error('🚀 开始获取文档所有文件...')

  try {
    // 1. 获取必要信息
    const jsSdkSession = getJsSdkSession()
    const token = getDocumentToken()

    console.error('📋 初始信息:', {
      hasSession: !!jsSdkSession,
      token: token.substring(0, 10) + '...'
    })

    // 2. 获取文档数据
    const { docVar } = await getDocumentData(token, jsSdkSession)

    // 3. 提取文件tokens
    const fileTokens = extractFileTokens(docVar)
    console.error(`📁 发现 ${fileTokens.length} 个文件`)

    if (fileTokens.length === 0) {
      console.error('ℹ️ 文档中没有文件')
      return {}
    }

    // 4. 获取CDN链接
    const cdnUrls = await getFileCdnUrls(fileTokens, jsSdkSession)
    console.error(`🔗 获取到 ${cdnUrls.length} 个文件的下载链接`)

    // 5. 下载所有文件
    const downloadedFiles: Record<string, string> = {}
    for (let i = 0; i < cdnUrls.length; i++) {
      const fileInfo = cdnUrls[i]
      console.error(`📥 正在下载第 ${i + 1}/${cdnUrls.length} 个文件: ${fileInfo.file_token}`)

      const fileData = await downloadAndDecryptFile(fileInfo, jsSdkSession)
      if (fileData) {
        downloadedFiles[fileInfo.file_token] = fileData
      }
    }

    console.error('🎉 所有文件下载完成，成功下载:', Object.keys(downloadedFiles).length)
    return downloadedFiles

  } catch (error) {
    console.error('❌ 获取文档文件失败:', error)
    throw error
  }
}
